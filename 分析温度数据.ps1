# PowerShell脚本分析测试13温度数据

function Convert-SecondsToTime {
    param([double]$seconds)
    $hours = [math]::Floor($seconds / 3600)
    $minutes = [math]::Floor(($seconds % 3600) / 60)
    $secs = [math]::Floor($seconds % 60)
    return "{0:D2}:{1:D2}:{2:D2}" -f $hours, $minutes, $secs
}

Write-Host "测试13温度数据统计分析"
Write-Host "=" * 50

# 读取CSV数据
$data = Import-Csv "13_fixed_headers_sensor_drop_repaired.csv" -Encoding UTF8

# 传感器映射
$sensorMapping = @{
    "遮阳罩表面温度" = "普通遮阳罩表面温度"
    "遮阳罩背面温度" = "普通遮阳罩背面温度"
    "遮阳罩皮革表面温度" = "遮阳罩皮革表面温度"
    "制冷帐篷表面温度" = "辐射制冷遮阳罩表面温度"
    "制冷帐篷背面温度" = "辐射制冷遮阳罩背面温度"
    "制冷帐篷皮革温度" = "制冷帐篷皮革温度"
    "皮革表面温度" = "直晒皮革表面温度"
    "皮革背面温度" = "直晒皮革背面温度"
}

# 分析每个传感器
foreach ($sensor in $sensorMapping.Keys) {
    $displayName = $sensorMapping[$sensor]
    
    # 获取有效数据
    $validData = $data | Where-Object { $_.$sensor -ne "" -and $_.$sensor -ne $null }
    
    if ($validData.Count -eq 0) { continue }
    
    # 转换为数值
    $temps = $validData | ForEach-Object { [double]$_.$sensor }
    $envTemps = $validData | ForEach-Object { [double]$_."环境温度" }
    
    # 基本统计
    $avgTemp = ($temps | Measure-Object -Average).Average
    $maxTemp = ($temps | Measure-Object -Maximum).Maximum
    $minTemp = ($temps | Measure-Object -Minimum).Minimum
    
    # 找到最高和最低温度的时间
    $maxIndex = 0
    $minIndex = 0
    for ($i = 0; $i -lt $temps.Count; $i++) {
        if ($temps[$i] -eq $maxTemp) { $maxIndex = $i; break }
    }
    for ($i = 0; $i -lt $temps.Count; $i++) {
        if ($temps[$i] -eq $minTemp) { $minIndex = $i; break }
    }
    
    $maxTime = Convert-SecondsToTime ([double]$validData[$maxIndex]."时间")
    $minTime = Convert-SecondsToTime ([double]$validData[$minIndex]."时间")
    
    # 计算与环境温度的最大温差
    $maxDiff = 0
    $maxDiffTime = "00:00:00"
    
    for ($i = 0; $i -lt $temps.Count; $i++) {
        $diff = [math]::Abs($temps[$i] - $envTemps[$i])
        if ($diff -gt $maxDiff) {
            $maxDiff = $diff
            $maxDiffTime = Convert-SecondsToTime ([double]$validData[$i]."时间")
        }
    }
    
    # 输出结果
    Write-Host "[曲线: $displayName]"
    Write-Host "    - 平均温度: $($avgTemp.ToString('F2')) °C"
    Write-Host "    - 最高温度: $($maxTemp.ToString('F2')) °C (时间: $maxTime)"
    Write-Host "    - 最低温度: $($minTemp.ToString('F2')) °C (时间: $minTime)"
    Write-Host "    - 与环境最大温差: $($maxDiff.ToString('F2')) °C (时间: $maxDiffTime)"
}

# 环境温度分析
Write-Host ""
Write-Host "环境温度分析"
Write-Host "-" * 30

$envData = $data | Where-Object { $_."环境温度" -ne "" -and $_."环境温度" -ne $null }
$envTemps = $envData | ForEach-Object { [double]$_."环境温度" }

$avgEnv = ($envTemps | Measure-Object -Average).Average
$maxEnv = ($envTemps | Measure-Object -Maximum).Maximum
$minEnv = ($envTemps | Measure-Object -Minimum).Minimum

# 找到最高和最低环境温度的时间
$maxEnvIndex = 0
$minEnvIndex = 0
for ($i = 0; $i -lt $envTemps.Count; $i++) {
    if ($envTemps[$i] -eq $maxEnv) { $maxEnvIndex = $i; break }
}
for ($i = 0; $i -lt $envTemps.Count; $i++) {
    if ($envTemps[$i] -eq $minEnv) { $minEnvIndex = $i; break }
}

$maxEnvTime = Convert-SecondsToTime ([double]$envData[$maxEnvIndex]."时间")
$minEnvTime = Convert-SecondsToTime ([double]$envData[$minEnvIndex]."时间")

Write-Host "[曲线: 环境温度]"
Write-Host "    - 平均温度: $($avgEnv.ToString('F2')) °C"
Write-Host "    - 最高温度: $($maxEnv.ToString('F2')) °C (时间: $maxEnvTime)"
Write-Host "    - 最低温度: $($minEnv.ToString('F2')) °C (时间: $minEnvTime)"
