#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def seconds_to_time_str(seconds):
    """将秒数转换为时:分:秒格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def analyze_data():
    print("测试13温度数据统计分析")
    print("=" * 50)
    
    # 读取数据
    data = []
    with open('13_fixed_headers_sensor_drop_repaired.csv', 'r', encoding='utf-8') as f:
        lines = f.readlines()
        headers = lines[0].strip().split(',')
        
        for line in lines[1:]:
            values = line.strip().split(',')
            row = {}
            for i, header in enumerate(headers):
                try:
                    if header == '时间':
                        row[header] = float(values[i])
                    else:
                        row[header] = float(values[i]) if values[i] else None
                except (ValueError, IndexError):
                    row[header] = None
            data.append(row)
    
    # 传感器映射
    sensor_mapping = {
        '遮阳罩表面温度': '普通遮阳罩表面温度',
        '遮阳罩背面温度': '普通遮阳罩背面温度', 
        '遮阳罩皮革表面温度': '遮阳罩皮革表面温度',
        '制冷帐篷表面温度': '辐射制冷遮阳罩表面温度',
        '制冷帐篷背面温度': '辐射制冷遮阳罩背面温度',
        '制冷帐篷皮革温度': '制冷帐篷皮革温度',
        '皮革表面温度': '直晒皮革表面温度',
        '皮革背面温度': '直晒皮革背面温度'
    }
    
    # 分析每个传感器
    for sensor, display_name in sensor_mapping.items():
        values = [row[sensor] for row in data if row[sensor] is not None]
        if not values:
            continue
            
        # 基本统计
        avg_temp = sum(values) / len(values)
        max_temp = max(values)
        min_temp = min(values)
        
        # 找时间
        max_time = min_time = "00:00:00"
        for row in data:
            if row[sensor] == max_temp:
                max_time = seconds_to_time_str(row['时间'])
                break
        for row in data:
            if row[sensor] == min_temp:
                min_time = seconds_to_time_str(row['时间'])
                break
        
        # 计算与环境温度最大温差
        max_diff = 0
        max_diff_time = "00:00:00"
        for row in data:
            if row[sensor] is not None and row['环境温度'] is not None:
                diff = abs(row[sensor] - row['环境温度'])
                if diff > max_diff:
                    max_diff = diff
                    max_diff_time = seconds_to_time_str(row['时间'])
        
        print(f"[曲线: {display_name}]")
        print(f"    - 平均温度: {avg_temp:.2f} °C")
        print(f"    - 最高温度: {max_temp:.2f} °C (时间: {max_time})")
        print(f"    - 最低温度: {min_temp:.2f} °C (时间: {min_time})")
        print(f"    - 与环境最大温差: {max_diff:.2f} °C (时间: {max_diff_time})")
    
    # 环境温度分析
    print("\n环境温度分析")
    print("-" * 30)
    
    env_values = [row['环境温度'] for row in data if row['环境温度'] is not None]
    if env_values:
        avg_env = sum(env_values) / len(env_values)
        max_env = max(env_values)
        min_env = min(env_values)
        
        max_env_time = min_env_time = "00:00:00"
        for row in data:
            if row['环境温度'] == max_env:
                max_env_time = seconds_to_time_str(row['时间'])
                break
        for row in data:
            if row['环境温度'] == min_env:
                min_env_time = seconds_to_time_str(row['时间'])
                break
        
        print(f"[曲线: 环境温度]")
        print(f"    - 平均温度: {avg_env:.2f} °C")
        print(f"    - 最高温度: {max_env:.2f} °C (时间: {max_env_time})")
        print(f"    - 最低温度: {min_env:.2f} °C (时间: {min_env_time})")

if __name__ == "__main__":
    analyze_data()
