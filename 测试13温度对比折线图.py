#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试13温度对比折线图
参考提供的图表样式绘制不同遮阳材质的隔热性能对比
"""

import matplotlib.pyplot as plt
import csv
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_temperature_comparison_plot():
    """创建温度对比折线图"""

    # 读取数据
    data = []
    with open('13_fixed_headers_sensor_drop_repaired.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 转换数值
            for key in row:
                try:
                    row[key] = float(row[key])
                except (ValueError, TypeError):
                    row[key] = None
            data.append(row)

    # 提取数据列
    time_hours = [row['时间'] / 3600 for row in data if row['时间'] is not None]

    # 提取各传感器数据
    sensors = {
        '遮阳罩表面温度': [row['遮阳罩表面温度'] for row in data if row['遮阳罩表面温度'] is not None],
        '遮阳罩背面温度': [row['遮阳罩背面温度'] for row in data if row['遮阳罩背面温度'] is not None],
        '制冷帐篷表面温度': [row['制冷帐篷表面温度'] for row in data if row['制冷帐篷表面温度'] is not None],
        '制冷帐篷背面温度': [row['制冷帐篷背面温度'] for row in data if row['制冷帐篷背面温度'] is not None],
        '遮阳罩皮革表面温度': [row['遮阳罩皮革表面温度'] for row in data if row['遮阳罩皮革表面温度'] is not None],
        '制冷帐篷皮革温度': [row['制冷帐篷皮革温度'] for row in data if row['制冷帐篷皮革温度'] is not None],
        '皮革表面温度': [row['皮革表面温度'] for row in data if row['皮革表面温度'] is not None],
        '皮革背面温度': [row['皮革背面温度'] for row in data if row['皮革背面温度'] is not None],
        '环境温度': [row['环境温度'] for row in data if row['环境温度'] is not None]
    }
    
    # 创建图形和子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
    fig.suptitle('不同遮阳材质的隔热性能对比', fontsize=16, fontweight='bold')
    
    # 第一个子图：遮阳罩性能对比
    ax1.plot(time_hours, sensors['遮阳罩表面温度'], 'b-', linewidth=1.5, label='普通遮阳罩表面温度')
    ax1.plot(time_hours, sensors['遮阳罩背面温度'], 'r-', linewidth=1.5, label='普通遮阳罩背面温度')
    ax1.plot(time_hours, sensors['制冷帐篷表面温度'], 'g-', linewidth=1.5, label='辐射制冷遮阳罩表面温度')
    ax1.plot(time_hours, sensors['制冷帐篷背面温度'], 'orange', linewidth=1.5, label='辐射制冷遮阳罩背面温度')
    ax1.plot(time_hours, sensors['环境温度'], 'k-', linewidth=1, label='环境温度')
    
    ax1.set_ylabel('温度 (°C)')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper right', fontsize=9)
    ax1.set_ylim(30, 65)
    
    # 第二个子图：皮革材料隔热对比
    ax2.plot(time_hours, sensors['遮阳罩皮革表面温度'], 'b-', linewidth=1.5, label='遮阳罩皮革表面温度')
    ax2.plot(time_hours, sensors['制冷帐篷皮革温度'], 'g-', linewidth=1.5, label='制冷帐篷皮革温度')
    ax2.plot(time_hours, sensors['皮革表面温度'], 'purple', linewidth=1.5, label='直晒皮革表面温度')
    ax2.plot(time_hours, sensors['皮革背面温度'], 'brown', linewidth=1.5, label='直晒皮革背面温度')
    ax2.plot(time_hours, sensors['环境温度'], 'k-', linewidth=1, label='环境温度')
    
    ax2.set_ylabel('温度 (°C)')
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper right', fontsize=9)
    ax2.set_ylim(30, 40)
    
    # 第三个子图：关键温度对比
    ax3.plot(time_hours, sensors['遮阳罩表面温度'], 'b-', linewidth=1.5, label='最高温度传感器(普通遮阳罩表面)')
    ax3.plot(time_hours, sensors['制冷帐篷皮革温度'], 'g-', linewidth=1.5, label='最稳定温度传感器(制冷帐篷皮革)')
    ax3.plot(time_hours, sensors['环境温度'], 'k-', linewidth=1, label='环境温度')
    
    ax3.set_xlabel('时间 (h)')
    ax3.set_ylabel('温度 (°C)')
    ax3.grid(True, alpha=0.3)
    ax3.legend(loc='upper right', fontsize=9)
    ax3.set_ylim(30, 65)
    
    # 设置X轴
    for ax in [ax1, ax2, ax3]:
        ax.set_xlim(0, 8)
        ax.set_xticks(range(0, 9))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('测试13_不同遮阳材质隔热性能对比.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    create_temperature_comparison_plot()
