#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试13温度数据统计分析
分析各个传感器的温度数据，包括平均值、最高值、最低值和与环境温度的最大温差
"""

import csv
import statistics

def seconds_to_time_str(seconds):
    """将秒数转换为时:分:秒格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def analyze_temperature_data():
    """分析测试13的温度数据"""

    # 读取数据
    data = []
    with open('13_fixed_headers_sensor_drop_repaired.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 转换数值
            for key in row:
                if key != '时间':
                    try:
                        row[key] = float(row[key])
                    except (ValueError, TypeError):
                        row[key] = None
                else:
                    row[key] = float(row[key])
            data.append(row)

    print("测试13温度数据统计分析")
    print("=" * 50)

    # 定义传感器名称映射
    sensor_mapping = {
        '遮阳罩表面温度': '普通遮阳罩表面温度',
        '遮阳罩背面温度': '普通遮阳罩背面温度',
        '遮阳罩皮革表面温度': '遮阳罩皮革表面温度',
        '制冷帐篷表面温度': '辐射制冷遮阳罩表面温度',
        '制冷帐篷背面温度': '辐射制冷遮阳罩背面温度',
        '制冷帐篷皮革温度': '制冷帐篷皮革温度',
        '皮革表面温度': '直晒皮革表面温度',
        '皮革背面温度': '直晒皮革背面温度'
    }

    # 分析每个传感器的数据
    for col_name, display_name in sensor_mapping.items():
        # 提取有效数据
        temp_values = [row[col_name] for row in data if row[col_name] is not None]
        env_values = [row['环境温度'] for row in data if row['环境温度'] is not None]

        if not temp_values:
            continue

        # 基本统计
        avg_temp = statistics.mean(temp_values)
        max_temp = max(temp_values)
        min_temp = min(temp_values)

        # 找到最高温度和最低温度的时间
        max_time = None
        min_time = None
        for row in data:
            if row[col_name] == max_temp:
                max_time = seconds_to_time_str(row['时间'])
                break
        for row in data:
            if row[col_name] == min_temp:
                min_time = seconds_to_time_str(row['时间'])
                break

        # 计算与环境温度的最大温差
        max_temp_diff = 0
        max_diff_time = "00:00:00"

        for row in data:
            if row[col_name] is not None and row['环境温度'] is not None:
                diff = abs(row[col_name] - row['环境温度'])
                if diff > max_temp_diff:
                    max_temp_diff = diff
                    max_diff_time = seconds_to_time_str(row['时间'])

        # 输出结果
        print(f"[曲线: {display_name}]")
        print(f"    - 平均温度: {avg_temp:.2f} °C")
        print(f"    - 最高温度: {max_temp:.2f} °C (时间: {max_time})")
        print(f"    - 最低温度: {min_temp:.2f} °C (时间: {min_time})")
        print(f"    - 与环境最大温差: {max_temp_diff:.2f} °C (时间: {max_diff_time})")

    # 分析环境温度
    print("\n环境温度分析")
    print("-" * 30)

    env_values = [row['环境温度'] for row in data if row['环境温度'] is not None]

    if env_values:
        avg_env = statistics.mean(env_values)
        max_env = max(env_values)
        min_env = min(env_values)

        # 找到最高和最低环境温度的时间
        max_env_time = None
        min_env_time = None
        for row in data:
            if row['环境温度'] == max_env:
                max_env_time = seconds_to_time_str(row['时间'])
                break
        for row in data:
            if row['环境温度'] == min_env:
                min_env_time = seconds_to_time_str(row['时间'])
                break

        print(f"[曲线: 环境温度]")
        print(f"    - 平均温度: {avg_env:.2f} °C")
        print(f"    - 最高温度: {max_env:.2f} °C (时间: {max_env_time})")
        print(f"    - 最低温度: {min_env:.2f} °C (时间: {min_env_time})")

if __name__ == "__main__":
    analyze_temperature_data()
