"""
传感器掉落检测分析脚本
====================

专门检测温度传感器掉落导致的曲线异常
传感器掉落的特征：
1. 温度突然大幅下降
2. 下降后温度接近环境温度
3. 可能出现异常的温度跳跃

作者：AI助手
日期：2025年
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
from datetime import datetime, timedelta
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def detect_sensor_drops():
    """检测传感器掉落"""
    
    print("=== 传感器掉落检测分析 ===\n")
    
    # 读取数据
    try:
        df = pd.read_csv('13_fixed_headers.csv')
        print(f"成功读取数据，共 {len(df)} 个数据点")
    except Exception as e:
        print(f"读取数据文件时出错: {e}")
        return
    
    # 计算真实时间
    total_duration_seconds = df['时间'].max()
    file_creation_time = datetime(2025, 7, 7, 17, 26, 0)
    test_start_time = file_creation_time - timedelta(seconds=total_duration_seconds)
    df['真实时间'] = df['时间'].apply(lambda x: test_start_time + timedelta(seconds=x))
    
    print(f"测试时间: {test_start_time.strftime('%H:%M:%S')} - {file_creation_time.strftime('%H:%M:%S')}")
    
    # 温度参数
    temp_columns = [
        '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
        '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
        '皮革表面温度', '皮革背面温度', '环境温度'
    ]
    
    drop_events = []
    
    print("=== 检测传感器掉落事件 ===")
    
    for col in temp_columns:
        if col in df.columns:
            events = detect_temperature_drops(df, col)
            if events:
                drop_events.extend(events)
                print(f"\n{col}:")
                for event in events:
                    print(f"  时间: {event['时间']} (索引 {event['索引']})")
                    print(f"  温度下降: {event['下降前温度']:.1f}°C → {event['下降后温度']:.1f}°C")
                    print(f"  下降幅度: {event['下降幅度']:.1f}°C")
                    print(f"  持续时间: {event['持续时间']:.0f}秒")
    
    if not drop_events:
        print("未检测到明显的传感器掉落事件")
    else:
        print(f"\n总共检测到 {len(drop_events)} 个可能的传感器掉落事件")
    
    # 创建可视化
    create_drop_detection_visualization(df, drop_events, temp_columns)
    
    # 分析掉落模式
    analyze_drop_patterns(df, drop_events)
    
    return df, drop_events

def detect_temperature_drops(df, column, drop_threshold=5.0, duration_threshold=60):
    """检测温度突然下降（可能的传感器掉落）"""
    
    data = df[column].dropna()
    if len(data) < 10:
        return []
    
    events = []
    
    # 计算温度变化率
    temp_diff = data.diff()
    
    # 寻找大幅下降
    significant_drops = temp_diff < -drop_threshold
    
    if not significant_drops.any():
        return events
    
    # 分析每个下降事件
    drop_indices = data[significant_drops].index.tolist()
    
    for drop_idx in drop_indices:
        if drop_idx == 0:
            continue
            
        before_temp = data.loc[drop_idx - 1]
        after_temp = data.loc[drop_idx]
        drop_magnitude = before_temp - after_temp
        
        # 检查下降后是否持续低温
        duration = check_low_temp_duration(df, column, drop_idx, after_temp)
        
        # 检查是否接近环境温度
        env_temp = df.loc[drop_idx, '环境温度'] if '环境温度' in df.columns else None
        near_env_temp = abs(after_temp - env_temp) < 5.0 if env_temp else False
        
        # 判断是否为传感器掉落
        if (drop_magnitude > drop_threshold and 
            (duration > duration_threshold or near_env_temp)):
            
            event = {
                '参数': column,
                '索引': drop_idx,
                '时间': df.loc[drop_idx, '真实时间'].strftime('%H:%M:%S'),
                '下降前温度': before_temp,
                '下降后温度': after_temp,
                '下降幅度': drop_magnitude,
                '持续时间': duration,
                '接近环境温度': near_env_temp
            }
            events.append(event)
    
    return events

def check_low_temp_duration(df, column, start_idx, low_temp, tolerance=2.0):
    """检查低温持续时间"""
    
    duration = 0
    current_idx = start_idx
    
    while current_idx < len(df) - 1:
        current_idx += 1
        current_temp = df.loc[current_idx, column]
        
        if pd.isna(current_temp):
            break
            
        if abs(current_temp - low_temp) <= tolerance:
            # 计算时间差（秒）
            time_diff = df.loc[current_idx, '时间'] - df.loc[start_idx, '时间']
            duration = time_diff
        else:
            break
    
    return duration

def create_drop_detection_visualization(df, drop_events, temp_columns):
    """创建传感器掉落检测可视化"""
    
    # 创建全景图
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('传感器掉落检测分析', fontsize=16, fontweight='bold')
    
    colors = ['#E74C3C', '#F39C12', '#D35400', '#3498DB', '#2980B9', 
              '#1ABC9C', '#9B59B6', '#8E44AD', '#27AE60']
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        if col in df.columns:
            # 绘制温度曲线
            ax.plot(df['真实时间'], df[col], color=colors[i], linewidth=1.5, alpha=0.8)
            
            # 标记掉落事件
            col_events = [e for e in drop_events if e['参数'] == col]
            for event in col_events:
                event_time = df.loc[event['索引'], '真实时间']
                event_temp = df.loc[event['索引'], col]
                
                ax.scatter(event_time, event_temp, color='red', s=100, 
                          marker='v', label='疑似掉落', zorder=5)
                
                # 添加注释
                ax.annotate(f'掉落{event["下降幅度"]:.1f}°C', 
                           xy=(event_time, event_temp),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                           fontsize=8)
            
            ax.set_title(col, fontsize=10, fontweight='bold')
            ax.set_ylabel('温度 (°C)', fontsize=9)
            ax.grid(True, alpha=0.3)
            
            # 设置时间格式
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.tick_params(axis='x', rotation=45, labelsize=8)
            
            if col_events:
                ax.legend(fontsize=8)
    
    plt.tight_layout()
    plt.savefig('传感器掉落检测分析.png', dpi=300, bbox_inches='tight')
    print("传感器掉落检测图已保存为: 传感器掉落检测分析.png")
    plt.close()
    
    # 创建重点关注图（如果有掉落事件）
    if drop_events:
        create_focused_drop_analysis(df, drop_events)

def create_focused_drop_analysis(df, drop_events):
    """创建重点掉落事件分析图"""
    
    # 按参数分组
    params_with_drops = list(set([e['参数'] for e in drop_events]))
    
    if not params_with_drops:
        return
    
    fig, axes = plt.subplots(len(params_with_drops), 1, figsize=(16, 4*len(params_with_drops)))
    if len(params_with_drops) == 1:
        axes = [axes]
    
    fig.suptitle('传感器掉落事件详细分析', fontsize=16, fontweight='bold')
    
    colors = ['#E74C3C', '#3498DB', '#F39C12', '#27AE60', '#9B59B6']
    
    for i, param in enumerate(params_with_drops):
        ax = axes[i]
        
        # 绘制该参数的温度曲线
        ax.plot(df['真实时间'], df[param], color=colors[i % len(colors)], 
               linewidth=2, label=param, alpha=0.8)
        
        # 绘制环境温度作为参考
        if '环境温度' in df.columns:
            ax.plot(df['真实时间'], df['环境温度'], color='gray', 
                   linewidth=1, linestyle='--', alpha=0.6, label='环境温度')
        
        # 标记该参数的所有掉落事件
        param_events = [e for e in drop_events if e['参数'] == param]
        for j, event in enumerate(param_events):
            event_time = df.loc[event['索引'], '真实时间']
            event_temp = df.loc[event['索引'], param]
            
            ax.scatter(event_time, event_temp, color='red', s=150, 
                      marker='v', zorder=5)
            
            # 详细注释
            ax.annotate(f'事件{j+1}\n下降{event["下降幅度"]:.1f}°C\n持续{event["持续时间"]:.0f}s', 
                       xy=(event_time, event_temp),
                       xytext=(20, 20), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                       arrowprops=dict(arrowstyle='->', color='red'),
                       fontsize=9)
        
        ax.set_title(f'{param} - 检测到 {len(param_events)} 个掉落事件', 
                    fontsize=12, fontweight='bold')
        ax.set_ylabel('温度 (°C)', fontsize=11)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 设置时间格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('传感器掉落详细分析.png', dpi=300, bbox_inches='tight')
    print("传感器掉落详细分析图已保存为: 传感器掉落详细分析.png")
    plt.close()

def analyze_drop_patterns(df, drop_events):
    """分析掉落模式"""
    
    if not drop_events:
        return
    
    print(f"\n=== 掉落模式分析 ===")
    
    # 按参数统计
    param_counts = {}
    for event in drop_events:
        param = event['参数']
        param_counts[param] = param_counts.get(param, 0) + 1
    
    print("各参数掉落次数:")
    for param, count in param_counts.items():
        print(f"  {param}: {count}次")
    
    # 时间分布分析
    drop_times = [datetime.strptime(e['时间'], '%H:%M:%S').hour for e in drop_events]
    print(f"\n掉落事件时间分布:")
    for hour in sorted(set(drop_times)):
        count = drop_times.count(hour)
        print(f"  {hour}:00-{hour}:59: {count}次")
    
    # 下降幅度统计
    drop_magnitudes = [e['下降幅度'] for e in drop_events]
    print(f"\n下降幅度统计:")
    print(f"  平均下降: {np.mean(drop_magnitudes):.1f}°C")
    print(f"  最大下降: {np.max(drop_magnitudes):.1f}°C")
    print(f"  最小下降: {np.min(drop_magnitudes):.1f}°C")

if __name__ == "__main__":
    df, drop_events = detect_sensor_drops()
    
    print(f"\n=== 检测完成 ===")
    if drop_events:
        print(f"检测到 {len(drop_events)} 个可能的传感器掉落事件")
        print("请查看生成的分析图表了解详细情况")
    else:
        print("未检测到明显的传感器掉落事件")
